import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FeatureFulltextViewerComponent } from './feature-fulltext-viewer.component'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { BehaviorSubject, of } from 'rxjs'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  FulltextFacade,
  ReviewParamService,
  IndexedDBHandlerService,
  FieldSelectorViewModel,
} from '@venio/data-access/review'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { VenioNotificationService } from '@venio/feature/notification'
import { NotificationService } from '@progress/kendo-angular-notification'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { DialogService } from '@progress/kendo-angular-dialog'
import {
  ReviewPanelFacade,
  UtilityPanelFacade,
} from '@venio/data-access/document-utility'

describe('FeatureFulltextViewerComponent', () => {
  let component: FeatureFulltextViewerComponent
  let fixture: ComponentFixture<FeatureFulltextViewerComponent>
  let fulltextFacadeMock: any
  let fetchMetadataSubject: BehaviorSubject<FieldSelectorViewModel[]>

  beforeEach(async () => {
    // Create BehaviorSubject for fetchMetadata$
    fetchMetadataSubject = new BehaviorSubject<FieldSelectorViewModel[]>([])

    // Mock FulltextFacade
    fulltextFacadeMock = {
      fetchMetadata$: fetchMetadataSubject,
      textSearchActionHandler: new BehaviorSubject(null),
      highlightGroupActionHandler: new BehaviorSubject(null),
      activateSimilarTermsHighlight$: new BehaviorSubject(null),
      fetchFulltext: jest
        .fn()
        .mockReturnValue(of({ data: 'mock fulltext data' })),
      fetchFulltextSettings: jest.fn().mockReturnValue(of({ data: {} })),
      fetchTextMetadata: jest.fn().mockReturnValue(of({ data: [] })),
    }

    await TestBed.configureTestingModule({
      imports: [FeatureFulltextViewerComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        VenioNotificationService,
        NotificationService,
        DialogService,
        ReviewPanelFacade,
        UtilityPanelFacade,
        { provide: FulltextFacade, useValue: fulltextFacadeMock },
        {
          provide: ReviewParamService,
          useValue: {
            projectId: of(123),
          },
        },
        {
          provide: IndexedDBHandlerService,
          useValue: {
            ifSpecificPartExists: jest.fn().mockResolvedValue(false),
            addParts: jest.fn(),
            getParts: jest.fn().mockResolvedValue([]),
            clearDB: jest.fn(),
          },
        },
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(FeatureFulltextViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })

  describe('onRefresh', () => {
    let fetchExtractedFulltextSpy: jest.SpyInstance
    let fetchOCRFulltextSpy: jest.SpyInstance

    beforeEach(() => {
      // Set up component with some initial state
      component.currentFileId = 123
      component.projectId = 456
      component.selectedFulltextType = 1

      // Spy on private methods that are called by fetchFulltext
      fetchExtractedFulltextSpy = jest
        .spyOn(component as any, 'fetchExtractedFulltext')
        .mockImplementation(() => Promise.resolve())
      fetchOCRFulltextSpy = jest
        .spyOn(component as any, 'fetchOCRFulltext')
        .mockImplementation(() => {})

      // Spy on the fetchMetadata$ next method
      jest.spyOn(fulltextFacadeMock.fetchMetadata$, 'next')
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('should return early when selectedTextFields is empty', () => {
      // GIVEN selectedTextFields is an empty array
      component['selectedTextFields'] = []

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchMetadata$ should not be called and fetchExtractedFulltext should not be called
      expect(fulltextFacadeMock.fetchMetadata$.next).not.toHaveBeenCalled()
      expect(fetchExtractedFulltextSpy).not.toHaveBeenCalled()
      expect(fetchOCRFulltextSpy).not.toHaveBeenCalled()
    })

    it('should return early when selectedTextFields length is 0', () => {
      // GIVEN selectedTextFields has length 0
      component['selectedTextFields'] = []

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchMetadata$ should not be called and fetchExtractedFulltext should not be called
      expect(fulltextFacadeMock.fetchMetadata$.next).not.toHaveBeenCalled()
      expect(fetchExtractedFulltextSpy).not.toHaveBeenCalled()
      expect(fetchOCRFulltextSpy).not.toHaveBeenCalled()
    })

    it('should call fetchMetadata$ and fetchExtractedFulltext when selectedTextFields has items and selectedFulltextType is 1', () => {
      // GIVEN selectedTextFields has some items and selectedFulltextType is 1
      const mockSelectedFields: FieldSelectorViewModel[] = [
        {
          displayFieldName: 'Field1',
          fieldDisplayOrder: 1,
          isCustomField: false,
          venioFieldId: 1,
        },
        {
          displayFieldName: 'Field2',
          fieldDisplayOrder: 2,
          isCustomField: true,
          venioFieldId: 2,
        },
      ]
      component['selectedTextFields'] = mockSelectedFields
      component.selectedFulltextType = 1

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchMetadata$ should be called with selectedTextFields
      expect(fulltextFacadeMock.fetchMetadata$.next).toHaveBeenCalledWith(
        mockSelectedFields
      )

      // AND fetchExtractedFulltext should be called with isRetry = true
      expect(fetchExtractedFulltextSpy).toHaveBeenCalledWith(true)
      expect(fetchOCRFulltextSpy).not.toHaveBeenCalled()
    })

    it('should call fetchMetadata$ and fetchOCRFulltext when selectedTextFields has items and selectedFulltextType is greater than 1', () => {
      // GIVEN selectedTextFields has some items and selectedFulltextType is greater than 1
      const mockSelectedFields: FieldSelectorViewModel[] = [
        {
          displayFieldName: 'Field1',
          fieldDisplayOrder: 1,
          isCustomField: false,
          venioFieldId: 1,
        },
      ]
      component['selectedTextFields'] = mockSelectedFields
      component.selectedFulltextType = 2

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchMetadata$ should be called with selectedTextFields
      expect(fulltextFacadeMock.fetchMetadata$.next).toHaveBeenCalledWith(
        mockSelectedFields
      )

      // AND fetchOCRFulltext should be called
      expect(fetchOCRFulltextSpy).toHaveBeenCalled()
      expect(fetchExtractedFulltextSpy).not.toHaveBeenCalled()
    })

    it('should pass the correct selectedTextFields to fetchMetadata$', () => {
      // GIVEN selectedTextFields with specific data
      const mockSelectedFields: FieldSelectorViewModel[] = [
        {
          displayFieldName: 'CustomField',
          fieldDisplayOrder: 5,
          isCustomField: true,
          venioFieldId: 100,
        },
      ]
      component['selectedTextFields'] = mockSelectedFields

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchMetadata$ should be called with the exact selectedTextFields
      expect(fulltextFacadeMock.fetchMetadata$.next).toHaveBeenCalledWith(
        mockSelectedFields
      )
      expect(fulltextFacadeMock.fetchMetadata$.next).toHaveBeenCalledTimes(1)
    })

    it('should call fetchExtractedFulltext with isRetry parameter set to true when selectedFulltextType is 1', () => {
      // GIVEN selectedTextFields has items and selectedFulltextType is 1
      component['selectedTextFields'] = [
        {
          displayFieldName: 'TestField',
          fieldDisplayOrder: 1,
          isCustomField: false,
          venioFieldId: 1,
        },
      ]
      component.selectedFulltextType = 1

      // WHEN onRefresh is called
      component.onRefresh()

      // THEN fetchExtractedFulltext should be called with isRetry = true
      expect(fetchExtractedFulltextSpy).toHaveBeenCalledWith(true)
      expect(fetchExtractedFulltextSpy).toHaveBeenCalledTimes(1)
    })
  })
})
